name: CI
on:
  push:
    branches:
      - main
  pull_request:
  merge_group:

permissions: write-all

jobs:
  test:
    runs-on: ${{ matrix.os }}
    name: test (Python ${{ matrix.python-version }} on ${{ matrix.os-label }})
    strategy:
      fail-fast: false
      matrix:
        python-version: [ "3.8", "3.9", "3.10", "3.11", "3.12" ]
        os: [ "ubuntu-latest" ]
        os-label: [ "Ubuntu" ]
        include:
          - { python-version: "3.8", os: "windows-latest", os-label: "Windows" }
          - { python-version: "3.8", os: "macos-latest", os-label: "macOS" }
          - { python-version: "3.8", os: "ubuntu-22.04", os-label: "Ubuntu" }
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "${{ matrix.python-version }}"
      - name: Install Dependencies
        run: |
          pip install uv 
          uv sync
      - name: Build
        run: |
          uv build
      - name: Check
        run: |
          uvx ruff check src/coze_mcp_server
          uvx ruff format --check
          uvx mypy .

  test_success:
    # this aggregates success state of all jobs listed in `needs`
    # this is the only required check to pass CI
    name: "Test success"
    if: always()
    runs-on: ubuntu-latest
    needs: [ test ]
    steps:
      - name: "Success"
        if: needs.test.result == 'success'
        run: true
        shell: bash
      - name: "Failure"
        if: needs.test.result != 'success'
        run: false
        shell: bash

  draft:
    runs-on: ubuntu-latest
    needs: test_success
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: release-drafter/release-drafter@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
